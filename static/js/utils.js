/**
 * 前端工具函数库
 * 包含错误处理、调试、格式化等通用功能
 */

// 错误处理工具
const ErrorHandler = {
    // 全局错误处理
    setupGlobalErrorHandler() {
        window.addEventListener('error', (event) => {
            console.error('Global Error:', {
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                error: event.error
            });
            
            // 在开发环境显示错误提示
            if (window.AppConfig && !window.AppConfig.features.enableErrorReporting) {
                this.showErrorToast(`错误: ${event.message}`);
            }
        });

        window.addEventListener('unhandledrejection', (event) => {
            console.error('Unhandled Promise Rejection:', event.reason);
            
            // 在开发环境显示错误提示
            if (window.AppConfig && !window.AppConfig.features.enableErrorReporting) {
                this.showErrorToast(`Promise错误: ${event.reason}`);
            }
        });
    },

    // 显示错误提示
    showErrorToast(message, duration = 5000) {
        // 创建错误提示元素
        const toast = document.createElement('div');
        toast.className = 'fixed top-4 right-4 bg-red-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 max-w-sm';
        toast.innerHTML = `
            <div class="flex items-center">
                <i class="fas fa-exclamation-triangle mr-2"></i>
                <span>${message}</span>
                <button class="ml-2 text-white hover:text-gray-200" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        
        document.body.appendChild(toast);
        
        // 自动移除
        setTimeout(() => {
            if (toast.parentElement) {
                toast.remove();
            }
        }, duration);
    },

    // API错误处理
    handleApiError(error, context = '') {
        console.error(`API Error ${context}:`, error);
        
        let message = '网络请求失败';
        if (error.message) {
            message = error.message;
        } else if (typeof error === 'string') {
            message = error;
        }
        
        this.showErrorToast(message);
        return message;
    }
};

// 调试工具
const DebugUtils = {
    // 日志级别
    LogLevel: {
        ERROR: 0,
        WARN: 1,
        INFO: 2,
        DEBUG: 3
    },

    currentLevel: 2, // 默认INFO级别

    // 设置日志级别
    setLogLevel(level) {
        this.currentLevel = level;
    },

    // 格式化日志
    log(level, message, data = null) {
        if (level > this.currentLevel) return;

        const timestamp = new Date().toISOString();
        const levelNames = ['ERROR', 'WARN', 'INFO', 'DEBUG'];
        const levelName = levelNames[level] || 'UNKNOWN';
        
        const logMessage = `[${timestamp}] [${levelName}] ${message}`;
        
        switch (level) {
            case this.LogLevel.ERROR:
                console.error(logMessage, data);
                break;
            case this.LogLevel.WARN:
                console.warn(logMessage, data);
                break;
            case this.LogLevel.INFO:
                console.info(logMessage, data);
                break;
            case this.LogLevel.DEBUG:
                console.debug(logMessage, data);
                break;
        }
    },

    // 便捷方法
    error(message, data) { this.log(this.LogLevel.ERROR, message, data); },
    warn(message, data) { this.log(this.LogLevel.WARN, message, data); },
    info(message, data) { this.log(this.LogLevel.INFO, message, data); },
    debug(message, data) { this.log(this.LogLevel.DEBUG, message, data); }
};

// 格式化工具
const FormatUtils = {
    // 格式化时间
    formatTime(timestamp) {
        if (!timestamp) return '';
        
        const date = new Date(timestamp);
        const now = new Date();
        const diffMs = now - date;
        const diffMins = Math.floor(diffMs / 60000);
        const diffHours = Math.floor(diffMs / 3600000);
        const diffDays = Math.floor(diffMs / 86400000);

        if (diffMins < 1) return '刚刚';
        if (diffMins < 60) return `${diffMins}分钟前`;
        if (diffHours < 24) return `${diffHours}小时前`;
        if (diffDays < 7) return `${diffDays}天前`;
        
        return date.toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    },

    // 格式化文件大小
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    // 截断文本
    truncateText(text, maxLength = 50) {
        if (!text || text.length <= maxLength) return text;
        return text.substring(0, maxLength) + '...';
    }
};

// API工具
const ApiUtils = {
    // 标准化API响应处理
    async handleResponse(response) {
        try {
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.message || `HTTP ${response.status}`);
            }
            
            // 检查业务状态码
            if (data.code && data.code !== 200) {
                throw new Error(data.message || '请求失败');
            }
            
            return data;
        } catch (error) {
            if (error instanceof SyntaxError) {
                throw new Error('服务器响应格式错误');
            }
            throw error;
        }
    },

    // 带重试的fetch
    async fetchWithRetry(url, options = {}, maxRetries = 3) {
        let lastError;
        
        for (let i = 0; i <= maxRetries; i++) {
            try {
                const response = await fetch(url, options);
                return await this.handleResponse(response);
            } catch (error) {
                lastError = error;
                
                if (i < maxRetries) {
                    // 指数退避
                    const delay = Math.pow(2, i) * 1000;
                    await new Promise(resolve => setTimeout(resolve, delay));
                    DebugUtils.warn(`API请求重试 ${i + 1}/${maxRetries}:`, { url, error: error.message });
                }
            }
        }
        
        throw lastError;
    }
};

// 性能监控工具
const PerformanceUtils = {
    // 性能标记
    marks: new Map(),

    // 开始计时
    mark(name) {
        this.marks.set(name, performance.now());
    },

    // 结束计时并返回耗时
    measure(name) {
        const startTime = this.marks.get(name);
        if (!startTime) {
            console.warn(`Performance mark '${name}' not found`);
            return 0;
        }
        
        const duration = performance.now() - startTime;
        this.marks.delete(name);
        
        DebugUtils.debug(`Performance: ${name} took ${duration.toFixed(2)}ms`);
        return duration;
    }
};

// 初始化工具
function initializeUtils() {
    // 设置全局错误处理
    ErrorHandler.setupGlobalErrorHandler();
    
    // 根据环境设置日志级别
    if (window.AppConfig) {
        DebugUtils.setLogLevel(
            window.AppConfig.features.enableDebugMode ? 
            DebugUtils.LogLevel.DEBUG : 
            DebugUtils.LogLevel.INFO
        );
    }
    
    DebugUtils.info('Utils initialized');
}

// 导出到全局
window.ErrorHandler = ErrorHandler;
window.DebugUtils = DebugUtils;
window.FormatUtils = FormatUtils;
window.ApiUtils = ApiUtils;
window.PerformanceUtils = PerformanceUtils;

// 页面加载完成后初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeUtils);
} else {
    initializeUtils();
}
