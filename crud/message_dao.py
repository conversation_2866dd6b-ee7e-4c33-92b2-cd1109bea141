import logging
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import func, and_, desc, asc
from typing import List, Optional, Tuple, Dict, Any
from datetime import datetime, timedelta

from models.message import Message
from models.conversation import Conversation
from utils.exceptions import DatabaseError, MessageNotFoundError

logger = logging.getLogger(__name__)


class MessageDao:
    """消息数据访问对象，提供优化的数据库查询"""

    @staticmethod
    def get_by_conversation_id(
        db: Session,
        conversation_id: int,
        page: int = 1,
        size: int = 50,
        order_desc: bool = False
    ) -> Tuple[List[Message], int]:
        """
        分页获取会话的消息列表

        Args:
            db: 数据库会话
            conversation_id: 会话ID
            page: 页码（从1开始）
            size: 每页大小
            order_desc: 是否按时间倒序

        Returns:
            Tuple[List[Message], int]: (消息列表, 总数)
        """
        try:
            query = db.query(Message).filter(Message.conversation_id == conversation_id)

            # 获取总数
            total = query.count()

            # 排序
            if order_desc:
                query = query.order_by(desc(Message.timestamp))
            else:
                query = query.order_by(asc(Message.timestamp))

            # 分页查询
            messages = (
                query.offset((page - 1) * size)
                .limit(size)
                .all()
            )

            logger.debug(f"Retrieved {len(messages)} messages for conversation {conversation_id}")
            return messages, total

        except Exception as e:
            logger.error(f"Failed to get messages for conversation {conversation_id}: {e}")
            raise DatabaseError(f"获取消息列表失败: {str(e)}")

    @staticmethod
    def get_by_id(db: Session, message_id: int) -> Optional[Message]:
        """
        根据ID获取消息

        Args:
            db: 数据库会话
            message_id: 消息ID

        Returns:
            Optional[Message]: 消息对象或None
        """
        try:
            message = db.query(Message).filter(Message.id == message_id).first()

            if message:
                logger.debug(f"Retrieved message {message_id}")
            else:
                logger.warning(f"Message {message_id} not found")

            return message

        except Exception as e:
            logger.error(f"Failed to get message {message_id}: {e}")
            raise DatabaseError(f"获取消息失败: {str(e)}")

    @staticmethod
    def create(
        db: Session,
        conversation_id: int,
        role: str,
        content: str,
        auto_commit: bool = True
    ) -> Message:
        """
        创建新消息

        Args:
            db: 数据库会话
            conversation_id: 会话ID
            role: 消息角色
            content: 消息内容
            auto_commit: 是否自动提交

        Returns:
            Message: 创建的消息对象
        """
        try:
            message = Message(
                conversation_id=conversation_id,
                role=role,
                content=content,
                timestamp=datetime.utcnow()
            )

            db.add(message)

            if auto_commit:
                db.commit()
                db.refresh(message)

            logger.debug(f"Created message {message.id} for conversation {conversation_id}")
            return message

        except Exception as e:
            if auto_commit:
                db.rollback()
            logger.error(f"Failed to create message for conversation {conversation_id}: {e}")
            raise DatabaseError(f"创建消息失败: {str(e)}")

    @staticmethod
    def update(
        db: Session,
        message_id: int,
        content: str = None,
        auto_commit: bool = True
    ) -> Optional[Message]:
        """
        更新消息内容

        Args:
            db: 数据库会话
            message_id: 消息ID
            content: 新内容
            auto_commit: 是否自动提交

        Returns:
            Optional[Message]: 更新后的消息对象
        """
        try:
            message = MessageDao.get_by_id(db, message_id)

            if not message:
                raise MessageNotFoundError(message_id)

            if content is not None:
                message.content = content

            if auto_commit:
                db.commit()
                db.refresh(message)

            logger.info(f"Updated message {message_id}")
            return message

        except MessageNotFoundError:
            raise
        except Exception as e:
            if auto_commit:
                db.rollback()
            logger.error(f"Failed to update message {message_id}: {e}")
            raise DatabaseError(f"更新消息失败: {str(e)}")

    @staticmethod
    def delete(
        db: Session,
        message_id: int,
        auto_commit: bool = True
    ) -> bool:
        """
        删除消息

        Args:
            db: 数据库会话
            message_id: 消息ID
            auto_commit: 是否自动提交

        Returns:
            bool: 删除成功返回True
        """
        try:
            message = MessageDao.get_by_id(db, message_id)

            if not message:
                raise MessageNotFoundError(message_id)

            db.delete(message)

            if auto_commit:
                db.commit()

            logger.info(f"Deleted message {message_id}")
            return True

        except MessageNotFoundError:
            raise
        except Exception as e:
            if auto_commit:
                db.rollback()
            logger.error(f"Failed to delete message {message_id}: {e}")
            raise DatabaseError(f"删除消息失败: {str(e)}")

    @staticmethod
    def get_conversation_stats(db: Session, conversation_id: int) -> Dict[str, Any]:
        """
        获取会话消息统计信息

        Args:
            db: 数据库会话
            conversation_id: 会话ID

        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            # 消息总数
            total_messages = db.query(func.count(Message.id)).filter(
                Message.conversation_id == conversation_id
            ).scalar()

            # 用户消息数
            user_messages = db.query(func.count(Message.id)).filter(
                and_(
                    Message.conversation_id == conversation_id,
                    Message.role == "user"
                )
            ).scalar()

            # 助手消息数
            assistant_messages = db.query(func.count(Message.id)).filter(
                and_(
                    Message.conversation_id == conversation_id,
                    Message.role == "assistant"
                )
            ).scalar()

            # 最后消息时间
            last_message_time = db.query(func.max(Message.timestamp)).filter(
                Message.conversation_id == conversation_id
            ).scalar()

            # 第一条消息时间
            first_message_time = db.query(func.min(Message.timestamp)).filter(
                Message.conversation_id == conversation_id
            ).scalar()

            return {
                "total_messages": total_messages or 0,
                "user_messages": user_messages or 0,
                "assistant_messages": assistant_messages or 0,
                "last_message_time": last_message_time.isoformat() if last_message_time else None,
                "first_message_time": first_message_time.isoformat() if first_message_time else None
            }

        except Exception as e:
            logger.error(f"Failed to get conversation stats for {conversation_id}: {e}")
            raise DatabaseError(f"获取会话统计信息失败: {str(e)}")

    @staticmethod
    def search_messages(
        db: Session,
        conversation_id: int,
        search_term: str,
        page: int = 1,
        size: int = 20
    ) -> Tuple[List[Message], int]:
        """
        搜索会话中的消息

        Args:
            db: 数据库会话
            conversation_id: 会话ID
            search_term: 搜索词
            page: 页码
            size: 每页大小

        Returns:
            Tuple[List[Message], int]: (消息列表, 总数)
        """
        try:
            query = db.query(Message).filter(
                and_(
                    Message.conversation_id == conversation_id,
                    Message.content.contains(search_term)
                )
            )

            total = query.count()

            messages = (
                query.order_by(desc(Message.timestamp))
                .offset((page - 1) * size)
                .limit(size)
                .all()
            )

            logger.debug(f"Found {len(messages)} messages matching '{search_term}' in conversation {conversation_id}")
            return messages, total

        except Exception as e:
            logger.error(f"Failed to search messages in conversation {conversation_id}: {e}")
            raise DatabaseError(f"搜索消息失败: {str(e)}")

    @staticmethod
    def bulk_create(
        db: Session,
        messages_data: List[Dict[str, Any]],
        auto_commit: bool = True
    ) -> List[Message]:
        """
        批量创建消息

        Args:
            db: 数据库会话
            messages_data: 消息数据列表
            auto_commit: 是否自动提交

        Returns:
            List[Message]: 创建的消息列表
        """
        try:
            messages = []
            for data in messages_data:
                message = Message(
                    conversation_id=data['conversation_id'],
                    role=data['role'],
                    content=data['content'],
                    timestamp=data.get('timestamp', datetime.utcnow())
                )
                messages.append(message)
                db.add(message)

            if auto_commit:
                db.commit()
                for message in messages:
                    db.refresh(message)

            logger.info(f"Bulk created {len(messages)} messages")
            return messages

        except Exception as e:
            if auto_commit:
                db.rollback()
            logger.error(f"Failed to bulk create messages: {e}")
            raise DatabaseError(f"批量创建消息失败: {str(e)}")
