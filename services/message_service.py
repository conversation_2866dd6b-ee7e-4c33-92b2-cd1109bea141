"""Message service for managing chat messages."""
import logging
import time
from typing import List, Generator, Dict

from sqlalchemy.orm import Session

from crud.conversation_dao import ConversationDao
from crud.message_dao import MessageDao
from models.message import Message
from schemas import ChatRequest
from services.llm_service import get_llm_service
logger = logging.getLogger(__name__)

class MessageService:

    def get_conversation_messages(
            self, db: Session, conversation_id: int, username: str
    ) -> List[Message]:
        """
        获取指定会话的所有消息
        """
        conversation = ConversationDao.get_by_id(db, conversation_id, username)
        if not conversation:
            return []
        message, total = MessageDao.get_by_conversation_id(db, conversation_id)
        return message

    def create_user_message(
            self, db: Session, conversation_id: int, content: str, username: str, parent_msg_id: int = 0
    ) -> Message:
        """
        创建用户消息
        """
        conversation = ConversationDao.get_by_id(db, conversation_id, username)
        if not conversation:
            ConversationDao.create(db, conversation_id, username, "新的对话")
        return MessageDao.create(db, conversation_id, "user", content, parent_msg_id)

    def create_assistant_message(
            self, db: Session, conversation_id: int, content: str, parent_msg_id: int = 0
    ) -> Message:
        """
        创建助手消息
        """
        return MessageDao.create(db, conversation_id, "assistant", content, parent_msg_id)

    def generate_chat_response(self, chat_data: ChatRequest) -> Generator[str, None, None]:
        """
        生成流式聊天回复（集成RAG）
        """
        try:
            # 使用LLM服务生成RAG回复
            llm_service = get_llm_service()
            yield from llm_service.generate_rag_response(chat_data.message, chat_data.collection_name, chat_data.input)
        except Exception as e:
            error_message = "抱歉，RAG服务暂时不可用！"
            logger.error(f"生成回复失败: {str(e)}")
            words = error_message.split()
            for word in words:
                yield word + " "
                time.sleep(0.1)

    def get_messages_by_conversation_id(
            self, db: Session, conversation_id: int, username: str
    ) -> Dict[int, List[Message]]:
        """
        获取格式化的消息数据
        """
        messages = self.get_conversation_messages(db, conversation_id, username)
        return {conversation_id: messages}
