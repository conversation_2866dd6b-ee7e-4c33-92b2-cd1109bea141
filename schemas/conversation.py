"""Conversation schemas for request/response validation."""

from datetime import datetime
from pydantic import BaseModel
from typing import Optional


class ConversationCreate(BaseModel):
    """Conversation creation request schema."""

    title: Optional[str] = "新的对话"


class ConversationResponse(BaseModel):
    """Conversation response schema."""

    id: int
    title: str
    created_at: datetime

    model_config = {"from_attributes": True}
