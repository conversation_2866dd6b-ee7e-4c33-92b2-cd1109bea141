"""Conversations API routes."""

from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from crud.database import get_database_session
from schemas.conversation import ConversationCreate, ConversationResponse
from schemas.response import StandardResponse, StandardListResponse, StandardErrorResponse
from services.conversation_service import ConversationService
from utils.dependencies import get_current_username

router = APIRouter(prefix="/api", tags=["会话管理"])
conversation_service = ConversationService()


@router.get(
    "/conversations", summary="获取对话列表"
)
async def get_conversations(
    current_username: str = Depends(get_current_username),
    db: Session = Depends(get_database_session),
):
    """获取当前用户的所有对话列表"""
    try:
        conversations = conversation_service.get_user_conversations(db, current_username)
        return StandardListResponse[ConversationResponse](
            code=200,
            message="success",
            data=conversations
        )
    except Exception as e:
        return StandardErrorResponse(
            code=1005,
            message="内部服务器错误",
            details=str(e)
        )


@router.post(
    "/conversations/new", summary="新建对话"
)
async def create_conversation(
    conversation_data: ConversationCreate,
    current_username: str = Depends(get_current_username),
    db: Session = Depends(get_database_session),
):
    """为当前用户创建新的对话"""
    try:
        conversation = conversation_service.create_conversation(
            db, current_username, conversation_data.title
        )
        return StandardResponse[ConversationResponse](
            code=200,
            message="success",
            data=conversation
        )
    except Exception as e:
        return StandardErrorResponse(
            code=1005,
            message="内部服务器错误",
            details=str(e)
        )


@router.delete("/conversations/{conversation_id}", summary="删除对话")
async def delete_conversation(
    conversation_id: int,
    current_username: str = Depends(get_current_username),
    db: Session = Depends(get_database_session),
):
    """删除指定的对话"""
    try:
        success = conversation_service.delete_conversation(
            db, conversation_id, current_username
        )

        if not success:
            return StandardErrorResponse(
                code=1004,
                message="对话不存在或无权限访问"
            )

        return StandardResponse[None](
            code=200,
            message="对话删除成功",
            data=None
        )
    except Exception as e:
        return StandardErrorResponse(
            code=1005,
            message="内部服务器错误",
            details=str(e)
        )